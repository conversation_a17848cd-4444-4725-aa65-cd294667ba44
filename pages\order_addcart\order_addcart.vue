<template>
	<PageLayout iconColor="#000" :isBackgroundColor="false" :isShowBack="false" :navTitle="navTitle"
		:bgMaskStyle="bgMaskStyleComputed">
		<!-- 页面内容 -->
		<view class="page-order-content">
			<view class="page-order-tags">
				<view class="page-order-tags-item">
					<view class="page-order-tags-item-icon">
						<image class="page-order-tags-item-icon-img"
							src="/static/images/8d229a503a8d959340f2005e837d5ac83c1fa9d5.svg" />
					</view>
					<view class="page-order-tags-item-text">债券</view>
				</view>
				<view class="page-order-tags-item">
					<view class="page-order-tags-item-icon">
						<image class="page-order-tags-item-icon-img" src="/static/images/discount.svg" />
					</view>
					<view class="page-order-tags-item-text">优惠</view>
				</view>
				<text class="page-order-tags-suffix">
					管理
				</text>
			</view>
			<!-- 商品列表 -->
			<view class="page-order-goods-list">
				<!-- 商品项1 -->
				<view class="goods-item-header">
					<view class="store-info">
						<view class="store-icon">
							<image class="store-icon-img"
								src="http://localhost:3845/assets/96dcb7fc8da20d1313902a97a0457ac96e412a17.svg" />
						</view>
						<text class="store-name">泡小燕旗舰店</text>
					</view>
				</view>
				<view class="goods-item">

					<view class="goods-content">
						<view class="goods-image">
							<image class="goods-image-img"
								src="http://localhost:3845/assets/a41caf3124464ca36ce4cd2f9d4a390a11447a8d.png" />
						</view>
						<view class="goods-info">
							<text class="goods-title">泡小燕原味燕窝胶原饮90g</text>
							<view class="goods-spec">
								<text class="goods-spec-text">原味胶原饮1盒90g</text>
								<image class="goods-spec-arrow"
									src="http://localhost:3845/assets/325e1b400fad80c289e7953d6a477587b2136793.svg" />
							</view>
							<view class="goods-tags">
								<view class="goods-tag">
									<text class="goods-tag-text">债券抵扣¥57.70</text>
								</view>
								<view class="goods-tag">
									<text class="goods-tag-text">优惠¥3.10</text>
								</view>
							</view>
							<view class="goods-bottom">
								<view class="goods-price">
									<text class="price-label">到手价</text>
									<text class="price-symbol">￥</text>
									<text class="price-value">164.00</text>
								</view>
								<view class="goods-quantity">
									<view class="quantity-btn quantity-minus">
										<image class="quantity-btn-img"
											src="http://localhost:3845/assets/25669df0a661319716b627f4b94cedbbba81f377.svg" />
									</view>
									<view class="quantity-input">
										<text class="quantity-text">1</text>
									</view>
									<view class="quantity-btn quantity-plus">
										<image class="quantity-btn-img"
											src="http://localhost:3845/assets/eb7b1806d29bd03e221c0fbba1f00746f43e5be6.svg" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 商品项2 -->
				<view class="goods-item">
					<view class="goods-content">
						<view class="goods-image">
							<image class="goods-image-img"
								src="http://localhost:3845/assets/44fc40d779a331bdcde7c08e9fef632316ca2a21.png" />
						</view>
						<view class="goods-info">
							<text class="goods-title">泡小燕雪梨饮6袋72g</text>
							<view class="goods-spec">
								<text class="goods-spec-text">雪梨饮6袋72g</text>
								<image class="goods-spec-arrow"
									src="http://localhost:3845/assets/325e1b400fad80c289e7953d6a477587b2136793.svg" />
							</view>
							<view class="goods-tags">
								<view class="goods-tag">
									<text class="goods-tag-text">债券抵扣¥57.70</text>
								</view>
								<view class="goods-tag">
									<text class="goods-tag-text">优惠¥3.10</text>
								</view>
							</view>
							<view class="goods-bottom">
								<view class="goods-price">
									<text class="price-label">到手价</text>
									<text class="price-symbol">￥</text>
									<text class="price-value">164.00</text>
								</view>
								<view class="goods-quantity">
									<view class="quantity-btn quantity-minus">
										<image class="quantity-btn-img"
											src="http://localhost:3845/assets/25669df0a661319716b627f4b94cedbbba81f377.svg" />
									</view>
									<view class="quantity-input">
										<text class="quantity-text">1</text>
									</view>
									<view class="quantity-btn quantity-plus">
										<image class="quantity-btn-img"
											src="http://localhost:3845/assets/eb7b1806d29bd03e221c0fbba1f00746f43e5be6.svg" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

	</PageLayout>
</template>

<script>
import PageLayout from "@/components/PageLayout/index.vue";

export default {
	components: {
		PageLayout
	},
	data() {
		return {
			navTitle: "购物车",
			value: [0, 2],
			range: [{ "value": 0, "text": "篮球" }, { "value": 1, "text": "足球" }, { "value": 2, "text": "游泳" }]
		}
	},
	methods: {
		change(e) {
			console.log(e)
		}
	},
	computed: {
		bgMaskStyleComputed() {
			return {
				"--bg-mask-background": `linear-gradient(180deg, #f7d7d6 2.04%, #F5F6FA 10.69%);`
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page-order-content {
	width: 100%;
	height: 100%;
	background-color: #f5f6fa;
	padding: 32rpx 16rpx;
}

.page-order-tags {
	width: 100%;
	height: 60rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.page-order-tags-item {
	width: fit-content;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8rpx 16rpx;
	background-color: #EBEDF0;
	border-radius: 8px;
	gap: 8rpx;

	&-icon {
		width: 32rpx;
		height: 32rpx;

		&-img {
			width: 100%;
			height: 100%;
		}
	}

	&-text {
		color: #666;
		font-size: 24rpx;
		font-weight: 400;
	}
}

.page-order-tags-suffix {
	color: #666;
	font-size: 28rpx;
	font-weight: 400;
	margin-left: auto;
}

.page-order-goods-list {
	width: 100%;
	margin-top: 32rpx;
}

.goods-item {
	width: 100%;
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 16rpx;
	padding: 16rpx;
}

.goods-item-header {
	width: 100%;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.store-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.store-icon {
	width: 24rpx;
	height: 24rpx;

	&-img {
		width: 100%;
		height: 100%;
	}
}

.store-name {
	color: #131313;
	font-size: 28rpx;
	font-weight: 500;
	font-family: 'PingFang SC', sans-serif;
}

.select-checkbox {
	width: 32rpx;
	height: 32rpx;

	&-img {
		width: 100%;
		height: 100%;
	}
}

.goods-content {
	width: 100%;
	display: flex;
	gap: 24rpx;
}

.goods-image {
	width: 176rpx;
	height: 176rpx;
	border-radius: 16rpx;
	overflow: hidden;
	flex-shrink: 0;

	&-img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.goods-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.goods-title {
	color: #131313;
	font-size: 28rpx;
	font-weight: 400;
	font-family: 'PingFang SC', sans-serif;
	line-height: 40rpx;
	margin-bottom: 8rpx;
}

.goods-spec {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 16rpx;

	&-text {
		color: #666666;
		font-size: 24rpx;
		font-weight: 400;
		font-family: 'PingFang SC', sans-serif;
	}

	&-arrow {
		width: 16rpx;
		height: 16rpx;
	}
}

.goods-tags {
	display: flex;
	gap: 16rpx;
	margin-bottom: 16rpx;
}

.goods-tag {
	padding: 4rpx 16rpx;
	background-color: rgba(255, 232, 232, 0.597);
	border-radius: 8rpx;

	&-text {
		color: #ff0000;
		font-size: 20rpx;
		font-weight: 400;
		font-family: 'PingFang SC', sans-serif;
	}
}

.goods-bottom {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.goods-price {
	display: flex;
	align-items: baseline;
	gap: 8rpx;
}

.price-label {
	color: #ff0000;
	font-size: 24rpx;
	font-weight: 400;
	font-family: 'PingFang SC', sans-serif;
}

.price-symbol {
	color: #ff0000;
	font-size: 28rpx;
	font-weight: 600;
	font-family: 'PingFang SC', sans-serif;
}

.price-value {
	color: #ff0000;
	font-size: 40rpx;
	font-weight: 600;
	font-family: 'PingFang SC', sans-serif;
}

.goods-quantity {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.quantity-btn {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;

	&-img {
		width: 24rpx;
		height: 24rpx;
	}
}

.quantity-input {
	width: 56rpx;
	height: 48rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.quantity-text {
	color: #131313;
	font-size: 24rpx;
	font-weight: 400;
	font-family: 'PingFang SC', sans-serif;
}
</style>